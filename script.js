/**
 * Script.js - Logic chính cho ứng dụng quản lý sách
 * Xử lý CRUD operations, Local Storage, và UI interactions
 */

// Constants
const STORAGE_KEY = 'books';
const ADMIN_PASSWORD_KEY = 'adminPassword';

// Utility Functions
function generateId() {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
}

function getCurrentYear() {
    return new Date().getFullYear();
}

// Local Storage Functions
function getBooksFromStorage() {
    try {
        const books = localStorage.getItem(STORAGE_KEY);
        return books ? JSON.parse(books) : [];
    } catch (error) {
        console.error('Lỗi khi đọc dữ liệu từ Local Storage:', error);
        return [];
    }
}

function saveBooksToStorage(books) {
    try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(books));
        return true;
    } catch (error) {
        console.error('Lỗi khi lưu dữ liệu vào Local Storage:', error);
        showMessage('Lỗi', 'Không thể lưu dữ liệu. Vui lòng thử lại.', 'error');
        return false;
    }
}

function getBookById(id) {
    const books = getBooksFromStorage();
    return books.find(book => book.id === id);
}

// Password Management
function saveAdminPassword(password) {
    try {
        // Mã hóa đơn giản (trong thực tế nên dùng hash mạnh hơn)
        const encoded = btoa(password);
        localStorage.setItem(ADMIN_PASSWORD_KEY, encoded);
        return true;
    } catch (error) {
        console.error('Lỗi khi lưu mật khẩu:', error);
        return false;
    }
}

function verifyAdminPassword(password) {
    try {
        const stored = localStorage.getItem(ADMIN_PASSWORD_KEY);
        if (!stored) {
            // Nếu chưa có mật khẩu nào được lưu, lưu mật khẩu đầu tiên
            return saveAdminPassword(password);
        }
        const decoded = atob(stored);
        return decoded === password;
    } catch (error) {
        console.error('Lỗi khi xác thực mật khẩu:', error);
        return false;
    }
}

// Book CRUD Operations
function addBook(bookData) {
    const books = getBooksFromStorage();
    
    const newBook = {
        id: generateId(),
        title: bookData.title.trim(),
        author: bookData.author.trim(),
        year: parseInt(bookData.year),
        category: bookData.category,
        pages: parseInt(bookData.pages),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };
    
    books.push(newBook);
    
    if (saveBooksToStorage(books)) {
        return newBook;
    }
    return null;
}

function updateBook(id, bookData) {
    const books = getBooksFromStorage();
    const index = books.findIndex(book => book.id === id);
    
    if (index === -1) {
        return null;
    }
    
    books[index] = {
        ...books[index],
        title: bookData.title.trim(),
        author: bookData.author.trim(),
        year: parseInt(bookData.year),
        category: bookData.category,
        pages: parseInt(bookData.pages),
        updatedAt: new Date().toISOString()
    };
    
    if (saveBooksToStorage(books)) {
        return books[index];
    }
    return null;
}

function deleteBook(id) {
    const books = getBooksFromStorage();
    const filteredBooks = books.filter(book => book.id !== id);
    
    if (filteredBooks.length === books.length) {
        return false; // Không tìm thấy sách để xóa
    }
    
    return saveBooksToStorage(filteredBooks);
}

// UI Helper Functions
function showMessage(title, message, type = 'info') {
    const modal = document.getElementById('messageModal');
    const titleElement = document.getElementById('messageTitle');
    const contentElement = document.getElementById('messageContent');
    
    if (modal && titleElement && contentElement) {
        titleElement.textContent = title;
        contentElement.textContent = message;
        
        // Thêm icon dựa trên type
        const icon = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
        titleElement.textContent = `${icon} ${title}`;
        
        modal.style.display = 'flex';
    } else {
        // Fallback nếu không có modal
        alert(`${title}: ${message}`);
    }
}

function closeMessageModal() {
    const modal = document.getElementById('messageModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN');
}

// Form Handling Functions
function getFormData(formId) {
    const form = document.getElementById(formId);
    if (!form) return null;
    
    const formData = new FormData(form);
    const data = {};
    
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }
    
    return data;
}

function populateForm(formId, data) {
    const form = document.getElementById(formId);
    if (!form) return;
    
    Object.keys(data).forEach(key => {
        const element = form.querySelector(`[name="${key}"]`);
        if (element) {
            element.value = data[key];
        }
    });
}

function clearForm(formId) {
    const form = document.getElementById(formId);
    if (form) {
        form.reset();
        clearAllErrors();
    }
}

// Add Book Form Functions
function initializeAddBookForm() {
    const form = document.getElementById('addBookForm');
    if (!form) return;
    
    // Setup real-time validation
    setupRealTimeValidation();
    
    // Handle form submission
    form.addEventListener('submit', handleAddBookSubmit);
    
    // Set current year as default
    const yearInput = document.getElementById('year');
    if (yearInput && !yearInput.value) {
        yearInput.value = getCurrentYear();
    }
}

function handleAddBookSubmit(event) {
    event.preventDefault();
    
    const formData = getFormData('addBookForm');
    if (!formData) return;
    
    // Validate form
    if (!validateBookForm(formData)) {
        showMessage('Lỗi', 'Vui lòng kiểm tra lại thông tin đã nhập', 'error');
        return;
    }
    
    // Verify admin password
    if (!verifyAdminPassword(formData.adminPassword)) {
        showFieldError('adminPassword', ['Mật khẩu quản trị không chính xác']);
        showMessage('Lỗi', 'Mật khẩu quản trị không chính xác', 'error');
        return;
    }
    
    // Add book
    const newBook = addBook(formData);
    if (newBook) {
        showMessage('Thành công', 'Đã thêm sách mới thành công!', 'success');
        
        // Redirect after a short delay
        setTimeout(() => {
            window.location.href = 'books.html';
        }, 1500);
    } else {
        showMessage('Lỗi', 'Không thể thêm sách. Vui lòng thử lại.', 'error');
    }
}

// Edit Book Form Functions
function initializeEditBookForm() {
    const form = document.getElementById('editBookForm');
    if (!form) return;

    // Setup real-time validation
    setupRealTimeValidation();

    // Handle form submission
    form.addEventListener('submit', handleEditBookSubmit);

    // Load book data from URL parameter
    loadBookForEdit();
}

function loadBookForEdit() {
    const urlParams = new URLSearchParams(window.location.search);
    const bookId = urlParams.get('id');

    if (!bookId) {
        showMessage('Lỗi', 'Không tìm thấy thông tin sách để chỉnh sửa', 'error');
        setTimeout(() => {
            window.location.href = 'books.html';
        }, 2000);
        return;
    }

    const book = getBookById(bookId);
    if (!book) {
        showMessage('Lỗi', 'Không tìm thấy sách với ID này', 'error');
        setTimeout(() => {
            window.location.href = 'books.html';
        }, 2000);
        return;
    }

    // Populate form with book data
    populateForm('editBookForm', {
        bookId: book.id,
        title: book.title,
        author: book.author,
        year: book.year,
        category: book.category,
        pages: book.pages
    });
}

function handleEditBookSubmit(event) {
    event.preventDefault();

    const formData = getFormData('editBookForm');
    if (!formData) return;

    // Validate form
    if (!validateBookForm(formData)) {
        showMessage('Lỗi', 'Vui lòng kiểm tra lại thông tin đã nhập', 'error');
        return;
    }

    // Verify admin password
    if (!verifyAdminPassword(formData.adminPassword)) {
        showFieldError('adminPassword', ['Mật khẩu quản trị không chính xác']);
        showMessage('Lỗi', 'Mật khẩu quản trị không chính xác', 'error');
        return;
    }

    // Update book
    const updatedBook = updateBook(formData.bookId, formData);
    if (updatedBook) {
        showMessage('Thành công', 'Đã cập nhật thông tin sách thành công!', 'success');

        // Redirect after a short delay
        setTimeout(() => {
            window.location.href = 'books.html';
        }, 1500);
    } else {
        showMessage('Lỗi', 'Không thể cập nhật sách. Vui lòng thử lại.', 'error');
    }
}

// Books List Functions
function initializeBooksPage() {
    loadBooksList();
    setupBooksPageEvents();
}

function setupBooksPageEvents() {
    // Search functionality
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(filterBooks, 300));
    }

    // Category filter
    const categoryFilter = document.getElementById('categoryFilter');
    if (categoryFilter) {
        categoryFilter.addEventListener('change', filterBooks);
    }
}

function loadBooksList() {
    const books = getBooksFromStorage();
    displayBooks(books);
    updateBooksStats(books);
}

function displayBooks(books) {
    const booksGrid = document.getElementById('booksGrid');
    const emptyState = document.getElementById('emptyState');

    if (!booksGrid) return;

    if (books.length === 0) {
        booksGrid.style.display = 'none';
        if (emptyState) emptyState.style.display = 'block';
        return;
    }

    if (emptyState) emptyState.style.display = 'none';
    booksGrid.style.display = 'grid';

    booksGrid.innerHTML = books.map(book => createBookCard(book)).join('');
}

function createBookCard(book) {
    return `
        <div class="book-card" data-book-id="${book.id}">
            <div class="book-category">${book.category}</div>
            <h3 class="book-title">${escapeHtml(book.title)}</h3>
            <p class="book-author">Tác giả: ${escapeHtml(book.author)}</p>

            <div class="book-details">
                <div class="book-detail">
                    <span class="book-detail-label">Năm xuất bản:</span>
                    <span class="book-detail-value">${book.year}</span>
                </div>
                <div class="book-detail">
                    <span class="book-detail-label">Số trang:</span>
                    <span class="book-detail-value">${book.pages}</span>
                </div>
            </div>

            <div class="book-actions">
                <button class="btn btn-secondary btn-small" onclick="editBook('${book.id}')">
                    <span class="icon">✏️</span>
                    Sửa
                </button>
                <button class="btn btn-danger btn-small" onclick="showDeleteModal('${book.id}')">
                    <span class="icon">🗑️</span>
                    Xóa
                </button>
            </div>
        </div>
    `;
}

function updateBooksStats(books) {
    const totalBooksElement = document.getElementById('totalBooks');
    if (totalBooksElement) {
        totalBooksElement.textContent = `Tổng số sách: ${books.length}`;
    }
}

function filterBooks() {
    const searchTerm = document.getElementById('searchInput')?.value.toLowerCase() || '';
    const categoryFilter = document.getElementById('categoryFilter')?.value || '';

    const books = getBooksFromStorage();
    const filteredBooks = books.filter(book => {
        const matchesSearch = !searchTerm ||
            book.title.toLowerCase().includes(searchTerm) ||
            book.author.toLowerCase().includes(searchTerm);

        const matchesCategory = !categoryFilter || book.category === categoryFilter;

        return matchesSearch && matchesCategory;
    });

    displayBooks(filteredBooks);
    updateBooksStats(filteredBooks);
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// HTML escape function for security
function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

// Book Actions
function editBook(bookId) {
    window.location.href = `edit-book.html?id=${bookId}`;
}

// Delete Modal Functions
let bookToDelete = null;

function showDeleteModal(bookId) {
    const book = getBookById(bookId);
    if (!book) {
        showMessage('Lỗi', 'Không tìm thấy sách để xóa', 'error');
        return;
    }

    bookToDelete = book;

    // Update modal content
    const titleElement = document.getElementById('deleteBookTitle');
    const authorElement = document.getElementById('deleteBookAuthor');
    const modal = document.getElementById('deleteModal');

    if (titleElement) titleElement.textContent = book.title;
    if (authorElement) authorElement.textContent = `Tác giả: ${book.author}`;

    // Clear password field and errors
    const passwordInput = document.getElementById('deletePassword');
    if (passwordInput) passwordInput.value = '';

    const errorElement = document.getElementById('deletePasswordError');
    if (errorElement) {
        errorElement.textContent = '';
        errorElement.style.display = 'none';
    }

    if (modal) modal.style.display = 'flex';
}

function closeDeleteModal() {
    const modal = document.getElementById('deleteModal');
    if (modal) modal.style.display = 'none';
    bookToDelete = null;
}

function confirmDelete() {
    if (!bookToDelete) {
        closeDeleteModal();
        return;
    }

    const passwordInput = document.getElementById('deletePassword');
    const password = passwordInput ? passwordInput.value : '';

    // Validate password
    const passwordErrors = validateSimplePassword(password);
    if (passwordErrors.length > 0) {
        showFieldError('deletePassword', passwordErrors);
        return;
    }

    // Verify admin password
    if (!verifyAdminPassword(password)) {
        showFieldError('deletePassword', ['Mật khẩu quản trị không chính xác']);
        return;
    }

    // Delete book
    if (deleteBook(bookToDelete.id)) {
        closeDeleteModal();
        showMessage('Thành công', 'Đã xóa sách thành công!', 'success');

        // Reload books list
        setTimeout(() => {
            loadBooksList();
            closeMessageModal();
        }, 1500);
    } else {
        showMessage('Lỗi', 'Không thể xóa sách. Vui lòng thử lại.', 'error');
    }
}

// Event Listeners for Modal
document.addEventListener('DOMContentLoaded', function() {
    // Close modals when clicking outside
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.addEventListener('click', function(event) {
            if (event.target === modal) {
                if (modal.id === 'deleteModal') {
                    closeDeleteModal();
                } else if (modal.id === 'messageModal') {
                    closeMessageModal();
                }
            }
        });
    });

    // Handle Enter key in delete password field
    const deletePasswordInput = document.getElementById('deletePassword');
    if (deletePasswordInput) {
        deletePasswordInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                confirmDelete();
            }
        });
    }

    // Handle Escape key to close modals
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeDeleteModal();
            closeMessageModal();
        }
    });
});

// Initialize functions based on current page
function initializePage() {
    const currentPage = window.location.pathname.split('/').pop();

    switch (currentPage) {
        case 'add-book.html':
            initializeAddBookForm();
            break;
        case 'edit-book.html':
            initializeEditBookForm();
            break;
        case 'books.html':
            initializeBooksPage();
            break;
        default:
            // Index page or other pages
            break;
    }
}

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initializePage);
