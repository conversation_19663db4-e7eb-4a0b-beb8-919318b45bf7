/* Reset và Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
header {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

header h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 10px;
    font-size: 2.5em;
    font-weight: 700;
}

.subtitle {
    text-align: center;
    color: #7f8c8d;
    font-size: 1.1em;
    margin-bottom: 20px;
}

nav {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.nav-link {
    color: #3498db;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 20px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-link:hover {
    background: #3498db;
    color: white;
    transform: translateY(-2px);
}

/* Main Content */
main {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Menu Styles */
.main-menu {
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
}

.menu-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
    max-width: 600px;
    width: 100%;
}

.menu-card h2 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.8em;
}

.menu-card p {
    color: #7f8c8d;
    margin-bottom: 30px;
    font-size: 1.1em;
}

.menu-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1em;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    min-width: 140px;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #7f8c8d, #6c7b7d);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(149, 165, 166, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
}

.icon {
    font-size: 1.2em;
}

/* Form Styles */
.form-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
}

.book-form {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.form-section {
    border: 1px solid #e0e0e0;
    border-radius: 15px;
    padding: 25px;
    background: rgba(248, 249, 250, 0.8);
}

.form-section h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.5em;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

.security-section {
    border-color: #f39c12;
    background: rgba(255, 248, 220, 0.8);
}

.security-section h2 {
    border-bottom-color: #f39c12;
    color: #d68910;
}

.security-note {
    color: #8e6a00;
    font-style: italic;
    margin-bottom: 15px;
    padding: 10px;
    background: rgba(255, 235, 59, 0.2);
    border-radius: 8px;
    border-left: 4px solid #f39c12;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
}

.required {
    color: #e74c3c;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 1em;
    transition: all 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.password-requirements {
    margin-top: 5px;
}

.password-requirements small {
    color: #7f8c8d;
    font-size: 0.85em;
}

.error-message {
    color: #e74c3c;
    font-size: 0.9em;
    margin-top: 5px;
    min-height: 20px;
    font-weight: 500;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
}

/* Books Page Styles */
.books-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.books-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.books-stats {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.1em;
}

.books-actions {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.search-input,
.filter-select {
    padding: 10px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    font-size: 1em;
    transition: all 0.3s ease;
    background: white;
}

.search-input {
    min-width: 250px;
}

.search-input:focus,
.filter-select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.books-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.book-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
}

.book-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.book-title {
    font-size: 1.3em;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 10px;
    line-height: 1.3;
}

.book-author {
    color: #7f8c8d;
    font-size: 1.1em;
    margin-bottom: 15px;
    font-style: italic;
}

.book-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 20px;
    font-size: 0.9em;
}

.book-detail {
    display: flex;
    flex-direction: column;
}

.book-detail-label {
    font-weight: 600;
    color: #34495e;
    margin-bottom: 2px;
}

.book-detail-value {
    color: #7f8c8d;
}

.book-category {
    display: inline-block;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.85em;
    font-weight: 600;
    margin-bottom: 15px;
}

.book-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.btn-small {
    padding: 8px 16px;
    font-size: 0.9em;
    min-width: auto;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #7f8c8d;
}

.empty-icon {
    font-size: 4em;
    margin-bottom: 20px;
}

.empty-state h3 {
    font-size: 1.5em;
    margin-bottom: 10px;
    color: #2c3e50;
}

.empty-state p {
    margin-bottom: 30px;
    font-size: 1.1em;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    border-radius: 20px;
    padding: 0;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 25px 30px 15px;
    border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
    color: #2c3e50;
    font-size: 1.4em;
    margin: 0;
}

.modal-body {
    padding: 25px 30px;
}

.modal-body p {
    margin-bottom: 15px;
    color: #7f8c8d;
    line-height: 1.6;
}

.book-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    margin: 15px 0;
    border-left: 4px solid #3498db;
}

.modal-actions {
    padding: 15px 30px 25px;
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    border-top: 1px solid #e0e0e0;
}

/* Info Section */
.info-section {
    margin-top: 30px;
    text-align: left;
}

.info-section h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.info-section ul {
    list-style: none;
    padding: 0;
}

.info-section li {
    padding: 8px 0;
    color: #7f8c8d;
    border-bottom: 1px solid #f0f0f0;
}

.info-section li:last-child {
    border-bottom: none;
}

/* Footer */
footer {
    text-align: center;
    padding: 20px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9em;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    .menu-card {
        padding: 25px;
    }
    
    .menu-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .form-container {
        padding: 20px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .books-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .books-actions {
        justify-content: center;
    }
    
    .search-input {
        min-width: auto;
        width: 100%;
    }
    
    .books-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .modal-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .nav {
        flex-direction: column;
        gap: 10px;
    }
    
    .book-details {
        grid-template-columns: 1fr;
    }
    
    .book-actions {
        justify-content: center;
    }
}
