/**
 * Validation.js - Xử lý validation cho form
 * <PERSON><PERSON><PERSON> các hàm validate cho từng trường và tổng thể form
 */

// Hàm validate tiêu đề sách
function validateTitle(title) {
    const errors = [];
    
    if (!title || title.trim() === '') {
        errors.push('Tiêu đề sách không được để trống');
    } else if (title.length > 100) {
        errors.push('Tiêu đề sách không được vượt quá 100 ký tự');
    }
    
    return errors;
}

// Hàm validate tác giả
function validateAuthor(author) {
    const errors = [];
    
    if (!author || author.trim() === '') {
        errors.push('Tác giả không được để trống');
    }
    
    return errors;
}

// Hàm validate năm xuất bản
function validateYear(year) {
    const errors = [];
    const currentYear = new Date().getFullYear();
    const yearNum = parseInt(year);
    
    if (!year || isNaN(yearNum)) {
        errors.push('Năm xuất bản phải là một số');
    } else if (yearNum < 1900) {
        errors.push('Năm xuất bản phải từ 1900 trở lên');
    } else if (yearNum > currentYear) {
        errors.push(`Năm xuất bản không được vượt quá ${currentYear}`);
    }
    
    return errors;
}

// Hàm validate số trang
function validatePages(pages) {
    const errors = [];
    const pagesNum = parseInt(pages);
    
    if (!pages || isNaN(pagesNum)) {
        errors.push('Số trang phải là một số');
    } else if (pagesNum <= 0) {
        errors.push('Số trang phải lớn hơn 0');
    } else if (!Number.isInteger(pagesNum)) {
        errors.push('Số trang phải là số nguyên');
    }
    
    return errors;
}

// Hàm validate thể loại
function validateCategory(category) {
    const errors = [];
    const validCategories = ['Văn học', 'Khoa học', 'Công nghệ', 'Thiếu nhi', 'Lịch sử', 'Tâm lý', 'Kinh tế', 'Khác'];
    
    if (!category || category.trim() === '') {
        errors.push('Vui lòng chọn thể loại sách');
    } else if (!validCategories.includes(category)) {
        errors.push('Thể loại không hợp lệ');
    }
    
    return errors;
}

// Hàm validate mật khẩu quản trị
function validateAdminPassword(password) {
    const errors = [];
    
    if (!password || password.trim() === '') {
        errors.push('Mật khẩu quản trị không được để trống');
        return errors;
    }
    
    // Kiểm tra độ dài tối thiểu
    if (password.length < 8) {
        errors.push('Mật khẩu phải có ít nhất 8 ký tự');
    }
    
    // Kiểm tra có chữ hoa
    if (!/[A-Z]/.test(password)) {
        errors.push('Mật khẩu phải chứa ít nhất 1 chữ cái viết hoa');
    }
    
    // Kiểm tra có chữ thường
    if (!/[a-z]/.test(password)) {
        errors.push('Mật khẩu phải chứa ít nhất 1 chữ cái viết thường');
    }
    
    // Kiểm tra có số
    if (!/[0-9]/.test(password)) {
        errors.push('Mật khẩu phải chứa ít nhất 1 chữ số');
    }
    
    // Kiểm tra có ký tự đặc biệt
    if (!/[!@#$%^&*]/.test(password)) {
        errors.push('Mật khẩu phải chứa ít nhất 1 ký tự đặc biệt (!@#$%^&*)');
    }
    
    return errors;
}

// Hàm validate xác nhận mật khẩu
function validateConfirmPassword(password, confirmPassword) {
    const errors = [];
    
    if (!confirmPassword || confirmPassword.trim() === '') {
        errors.push('Xác nhận mật khẩu không được để trống');
    } else if (password !== confirmPassword) {
        errors.push('Xác nhận mật khẩu không khớp với mật khẩu quản trị');
    }
    
    return errors;
}

// Hàm hiển thị lỗi cho một trường
function showFieldError(fieldId, errors) {
    const errorElement = document.getElementById(fieldId + 'Error');
    if (errorElement) {
        if (errors.length > 0) {
            errorElement.textContent = errors[0]; // Hiển thị lỗi đầu tiên
            errorElement.style.display = 'block';
        } else {
            errorElement.textContent = '';
            errorElement.style.display = 'none';
        }
    }
}

// Hàm xóa tất cả lỗi
function clearAllErrors() {
    const errorElements = document.querySelectorAll('.error-message');
    errorElements.forEach(element => {
        element.textContent = '';
        element.style.display = 'none';
    });
}

// Hàm validate toàn bộ form
function validateBookForm(formData) {
    const errors = {};
    
    // Validate từng trường
    errors.title = validateTitle(formData.title);
    errors.author = validateAuthor(formData.author);
    errors.year = validateYear(formData.year);
    errors.pages = validatePages(formData.pages);
    errors.category = validateCategory(formData.category);
    errors.adminPassword = validateAdminPassword(formData.adminPassword);
    errors.confirmPassword = validateConfirmPassword(formData.adminPassword, formData.confirmPassword);
    
    // Hiển thị lỗi cho từng trường
    Object.keys(errors).forEach(field => {
        showFieldError(field, errors[field]);
    });
    
    // Kiểm tra xem có lỗi nào không
    const hasErrors = Object.values(errors).some(fieldErrors => fieldErrors.length > 0);
    
    return !hasErrors;
}

// Hàm validate real-time khi người dùng nhập
function setupRealTimeValidation() {
    const fields = [
        { id: 'title', validator: validateTitle },
        { id: 'author', validator: validateAuthor },
        { id: 'year', validator: validateYear },
        { id: 'pages', validator: validatePages },
        { id: 'category', validator: validateCategory },
        { id: 'adminPassword', validator: validateAdminPassword }
    ];
    
    fields.forEach(field => {
        const element = document.getElementById(field.id);
        if (element) {
            element.addEventListener('blur', function() {
                const errors = field.validator(this.value);
                showFieldError(field.id, errors);
            });
            
            // Xóa lỗi khi người dùng bắt đầu nhập lại
            element.addEventListener('input', function() {
                if (this.value.trim() !== '') {
                    showFieldError(field.id, []);
                }
            });
        }
    });
    
    // Xử lý riêng cho confirm password
    const confirmPasswordElement = document.getElementById('confirmPassword');
    const adminPasswordElement = document.getElementById('adminPassword');
    
    if (confirmPasswordElement && adminPasswordElement) {
        confirmPasswordElement.addEventListener('blur', function() {
            const errors = validateConfirmPassword(adminPasswordElement.value, this.value);
            showFieldError('confirmPassword', errors);
        });
        
        confirmPasswordElement.addEventListener('input', function() {
            if (this.value.trim() !== '') {
                showFieldError('confirmPassword', []);
            }
        });
        
        // Kiểm tra lại confirm password khi admin password thay đổi
        adminPasswordElement.addEventListener('input', function() {
            if (confirmPasswordElement.value.trim() !== '') {
                const errors = validateConfirmPassword(this.value, confirmPasswordElement.value);
                showFieldError('confirmPassword', errors);
            }
        });
    }
}

// Hàm validate mật khẩu đơn giản (cho modal xóa)
function validateSimplePassword(password) {
    const errors = [];
    
    if (!password || password.trim() === '') {
        errors.push('Vui lòng nhập mật khẩu quản trị');
    }
    
    return errors;
}

// Export các hàm để sử dụng ở file khác
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        validateTitle,
        validateAuthor,
        validateYear,
        validatePages,
        validateCategory,
        validateAdminPassword,
        validateConfirmPassword,
        validateBookForm,
        validateSimplePassword,
        setupRealTimeValidation,
        showFieldError,
        clearAllErrors
    };
}
