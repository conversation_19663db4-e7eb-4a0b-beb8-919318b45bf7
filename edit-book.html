<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chỉnh Sửa Sách - <PERSON></title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>✏️ Chỉnh Sửa Thông Tin Sách</h1>
            <nav>
                <a href="index.html" class="nav-link">🏠 Trang chủ</a>
                <a href="books.html" class="nav-link">📋 <PERSON><PERSON> sách sách</a>
            </nav>
        </header>

        <main>
            <div class="form-container">
                <form id="editBookForm" class="book-form">
                    <input type="hidden" id="bookId" name="bookId">
                    
                    <div class="form-section">
                        <h2>Thông tin sách</h2>
                        
                        <div class="form-group">
                            <label for="title">Tiêu đề sách <span class="required">*</span></label>
                            <input type="text" id="title" name="title" maxlength="100" required>
                            <div class="error-message" id="titleError"></div>
                        </div>

                        <div class="form-group">
                            <label for="author">Tác giả <span class="required">*</span></label>
                            <input type="text" id="author" name="author" required>
                            <div class="error-message" id="authorError"></div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="year">Năm xuất bản <span class="required">*</span></label>
                                <input type="number" id="year" name="year" min="1900" max="2025" required>
                                <div class="error-message" id="yearError"></div>
                            </div>

                            <div class="form-group">
                                <label for="pages">Số trang <span class="required">*</span></label>
                                <input type="number" id="pages" name="pages" min="1" required>
                                <div class="error-message" id="pagesError"></div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="category">Thể loại <span class="required">*</span></label>
                            <select id="category" name="category" required>
                                <option value="">-- Chọn thể loại --</option>
                                <option value="Văn học">Văn học</option>
                                <option value="Khoa học">Khoa học</option>
                                <option value="Công nghệ">Công nghệ</option>
                                <option value="Thiếu nhi">Thiếu nhi</option>
                                <option value="Lịch sử">Lịch sử</option>
                                <option value="Tâm lý">Tâm lý</option>
                                <option value="Kinh tế">Kinh tế</option>
                                <option value="Khác">Khác</option>
                            </select>
                            <div class="error-message" id="categoryError"></div>
                        </div>
                    </div>

                    <div class="form-section security-section">
                        <h2>🔐 Xác thực quản trị</h2>
                        <p class="security-note">Vui lòng nhập mật khẩu quản trị để xác nhận thay đổi</p>
                        
                        <div class="form-group">
                            <label for="adminPassword">Mật khẩu quản trị <span class="required">*</span></label>
                            <input type="password" id="adminPassword" name="adminPassword" required>
                            <div class="password-requirements">
                                <small>Yêu cầu: Ít nhất 8 ký tự, có chữ hoa, chữ thường, số và ký tự đặc biệt (!@#$%^&*)</small>
                            </div>
                            <div class="error-message" id="adminPasswordError"></div>
                        </div>

                        <div class="form-group">
                            <label for="confirmPassword">Xác nhận mật khẩu <span class="required">*</span></label>
                            <input type="password" id="confirmPassword" name="confirmPassword" required>
                            <div class="error-message" id="confirmPasswordError"></div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="window.location.href='books.html'">
                            Hủy bỏ
                        </button>
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <span class="icon">💾</span>
                            Cập nhật sách
                        </button>
                    </div>
                </form>
            </div>
        </main>
    </div>

    <script src="validation.js"></script>
    <script src="script.js"></script>
    <script>
        // Khởi tạo form chỉnh sửa sách
        document.addEventListener('DOMContentLoaded', function() {
            initializeEditBookForm();
        });
    </script>
</body>
</html>
