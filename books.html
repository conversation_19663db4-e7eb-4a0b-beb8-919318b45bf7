<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Danh Sách <PERSON>ách - Quản L<PERSON>ch</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>📋 Danh S<PERSON>ch Sách</h1>
            <nav>
                <a href="index.html" class="nav-link">🏠 Trang chủ</a>
                <a href="add-book.html" class="nav-link">➕ Thêm sách</a>
            </nav>
        </header>

        <main>
            <div class="books-container">
                <div class="books-header">
                    <div class="books-stats">
                        <span id="totalBooks">Tổng số sách: 0</span>
                    </div>
                    <div class="books-actions">
                        <input type="text" id="searchInput" placeholder="🔍 Tìm kiếm sách..." class="search-input">
                        <select id="categoryFilter" class="filter-select">
                            <option value="">Tất cả thể loại</option>
                            <option value="Văn học">Văn học</option>
                            <option value="Khoa học">Khoa học</option>
                            <option value="Công nghệ">Công nghệ</option>
                            <option value="Thiếu nhi">Thiếu nhi</option>
                            <option value="Lịch sử">Lịch sử</option>
                            <option value="Tâm lý">Tâm lý</option>
                            <option value="Kinh tế">Kinh tế</option>
                            <option value="Khác">Khác</option>
                        </select>
                    </div>
                </div>

                <div id="booksGrid" class="books-grid">
                    <!-- Danh sách sách sẽ được render ở đây -->
                </div>

                <div id="emptyState" class="empty-state" style="display: none;">
                    <div class="empty-icon">📚</div>
                    <h3>Chưa có sách nào trong kho</h3>
                    <p>Hãy thêm sách đầu tiên vào hệ thống</p>
                    <a href="add-book.html" class="btn btn-primary">
                        <span class="icon">➕</span>
                        Thêm sách mới
                    </a>
                </div>
            </div>
        </main>
    </div>

    <!-- Modal xác nhận xóa -->
    <div id="deleteModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>⚠️ Xác nhận xóa sách</h3>
            </div>
            <div class="modal-body">
                <p>Bạn có chắc chắn muốn xóa sách này không?</p>
                <div class="book-info">
                    <strong id="deleteBookTitle"></strong><br>
                    <span id="deleteBookAuthor"></span>
                </div>
                
                <div class="form-group">
                    <label for="deletePassword">Mật khẩu quản trị <span class="required">*</span></label>
                    <input type="password" id="deletePassword" placeholder="Nhập mật khẩu để xác nhận">
                    <div class="error-message" id="deletePasswordError"></div>
                </div>
            </div>
            <div class="modal-actions">
                <button type="button" class="btn btn-secondary" onclick="closeDeleteModal()">Hủy bỏ</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">Xóa sách</button>
            </div>
        </div>
    </div>

    <!-- Modal thông báo -->
    <div id="messageModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="messageTitle"></h3>
            </div>
            <div class="modal-body">
                <p id="messageContent"></p>
            </div>
            <div class="modal-actions">
                <button type="button" class="btn btn-primary" onclick="closeMessageModal()">Đóng</button>
            </div>
        </div>
    </div>

    <script src="validation.js"></script>
    <script src="script.js"></script>
    <script>
        // Khởi tạo trang danh sách sách
        document.addEventListener('DOMContentLoaded', function() {
            initializeBooksPage();
        });
    </script>
</body>
</html>
